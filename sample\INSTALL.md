# DZMM插件安装指南

## 快速开始

### 1. 安装插件

将整个插件文件夹复制到你的astrbot插件目录中：

```
astrbot/
├── plugins/
│   └── astrbot_plugin_dzmm/
│       ├── main.py
│       ├── metadata.yaml
│       ├── _conf_schema.json
│       ├── README.md
│       └── INSTALL.md
```

### 2. 配置插件

在astrbot的Web管理界面中：

1. 进入"插件管理"页面
2. 找到"astrbot_plugin_dzmm"插件
3. 点击"配置"按钮
4. 填写必要的配置项：

#### 必需配置
- **API密钥 (api_key)**: 你的OpenAI API密钥

#### 可选配置
- **系统提示词 (system_prompt)**: 自定义AI的角色和行为
- **上下文长度 (context_length)**: 保留的历史消息数量（默认10条）
- **API地址 (api_url)**: 如果使用其他兼容接口
- **模型名称 (model)**: 要使用的AI模型
- **温度参数 (temperature)**: 控制回复的随机性（0-1）
- **最大Token数 (max_tokens)**: 单次回复的最大长度
- **Top-p参数 (top_p)**: 核采样参数（0-1）
- **重复惩罚 (repetition_penalty)**: 避免重复内容的系数

### 3. 启用插件

1. 在插件列表中找到"astrbot_plugin_dzmm"
2. 点击"启用"按钮
3. 重启astrbot服务

### 4. 开始使用

在聊天中输入以下命令：

```
/dzmm 你好，请介绍一下自己
/dzmm help
/dzmm clear
```

## 详细配置说明

### API密钥获取

1. **OpenAI官方**: 访问 https://platform.openai.com/api-keys
2. **第三方服务**: 根据你选择的服务商获取密钥

### 系统提示词示例

```
你是一个专业的编程助手，擅长Python、JavaScript等编程语言。请用简洁明了的方式回答问题，并在适当时提供代码示例。
```

```
你是一个友善的聊天机器人，喜欢用轻松幽默的语调与用户交流。请保持积极正面的态度。
```

### 上下文长度建议

- **轻度使用**: 5-10条消息
- **正常使用**: 10-20条消息  
- **深度对话**: 20-50条消息

注意：上下文越长，API调用成本越高。

### 模型选择

根据你的API服务商选择合适的模型：

- **OpenAI**: gpt-3.5-turbo, gpt-4, gpt-4-turbo
- **其他服务**: 根据服务商文档选择

## 故障排除

### 常见问题

#### 1. 插件无法启动
- 检查API密钥是否正确配置
- 查看astrbot日志中的错误信息
- 确认插件文件完整

#### 2. AI无回复
- 检查API密钥是否有效
- 确认API服务是否正常
- 查看网络连接状态

#### 3. 上下文丢失
- 检查上下文长度设置
- 确认用户标识是否正确
- 使用 `/dzmm clear` 重置上下文

#### 4. 回复质量差
- 调整温度参数（降低随机性）
- 优化系统提示词
- 尝试不同的模型

### 日志查看

在astrbot日志中查找以下关键词：
- `DZMM插件`
- `API请求错误`
- `解析JSON时出错`

### 性能优化

1. **减少API调用成本**:
   - 适当降低上下文长度
   - 减少max_tokens设置
   - 选择成本较低的模型

2. **提高响应速度**:
   - 选择响应更快的API服务
   - 减少max_tokens设置
   - 优化网络连接

## 更新插件

1. 备份当前配置
2. 下载新版本插件文件
3. 替换插件文件夹
4. 恢复配置设置
5. 重启astrbot

## 卸载插件

1. 在插件管理中禁用插件
2. 删除插件文件夹
3. 重启astrbot

## 技术支持

如果遇到问题，请：

1. 查看本文档的故障排除部分
2. 检查astrbot和插件的日志
3. 在项目仓库中提交Issue
4. 提供详细的错误信息和配置

## 版本兼容性

- astrbot版本要求: >= 2.0.0
- Python版本要求: >= 3.8
- 依赖包: requests, aiohttp

确保你的环境满足这些要求。
