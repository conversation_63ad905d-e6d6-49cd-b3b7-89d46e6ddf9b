# 使用示例

## 基本使用

### 1. 使用BV号
```
/bs BV1jv7YzJED2
/bs 1jv7YzJED2
```

### 2. 使用AV号
```
/bs av123456
/bs 123456
```

### 3. 使用完整链接
```
/bs https://www.bilibili.com/video/BV1jv7YzJED2
/bs https://m.bilibili.com/video/BV1jv7YzJED2
```

### 4. 使用短链接
```
/bs https://b23.tv/xxxxx
```

### 5. 带参数的链接
```
/bs https://www.bilibili.com/video/BV1jv7YzJED2?p=1&t=30
```

## 智能解析模式

### 6. 引用消息解析
```
# 场景：用户A发送包含bilibili链接的消息
用户A: 看看这个视频 https://www.bilibili.com/video/BV1jv7YzJED2

# 用户B引用用户A的消息并发送 /bs
用户B: [引用用户A的消息] /bs

# 插件会自动从引用的消息中提取链接并处理
```

### 7. 转发消息解析
```
# 场景：用户转发bilibili视频卡片或包含链接的消息
用户: [转发消息：哔哩哔哩视频分享]
用户: /bs

# 插件会自动从转发的消息中提取视频信息
```

### 8. 消息中包含链接
```
# 场景：在同一条消息中包含链接和命令
用户: 看看这个视频 https://www.bilibili.com/video/BV1jv7YzJED2 很有趣 /bs

# 插件会自动提取消息中的链接
```

## 配置示例

### OpenAI API配置
```json
{
  "openai_api_key": "sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
  "openai_api_url": "https://api.openai.com/v1/chat/completions",
  "openai_model": "gpt-3.5-turbo"
}
```

### 其他兼容API配置
```json
{
  "openai_api_key": "your-api-key",
  "openai_api_url": "https://api.deepseek.com/v1/chat/completions",
  "openai_model": "deepseek-chat"
}
```

### Bilibili配置
```json
{
  "bilibili_sessdata": "your-sessdata-from-browser-cookie"
}
```

## 预期输出示例

```
🔍 正在处理视频 BV1jv7YzJED2，请稍候...

📺 视频标题：[Kotoha] フログリ差し入れ开封の儀！

📋 内容总结：
这是一个关于Kotoha开箱礼物的视频。视频中主要内容包括：

1. 主播Kotoha收到了观众送的礼物
2. 开箱过程中展示了各种有趣的物品
3. 主播对每个礼物都表达了感谢
4. 整个过程充满了欢乐的互动

视频展现了主播与观众之间的温馨互动，体现了直播社区的友好氛围。
```

## 错误处理示例

### 无效链接格式
```
输入: /bs 无效链接
输出: ❌ 无法识别的视频链接或ID格式，请检查后重试
```

### 未找到链接（智能解析模式）
```
输入: /bs （没有引用消息，没有转发消息，消息中也没有链接）
输出: 使用方法：
1. /bs [视频链接或BV号]
2. 引用包含bilibili链接的消息后发送 /bs
3. 转发bilibili视频卡片后发送 /bs
...
```

### 未配置API密钥
```
输出: ❌ 未配置OpenAI API密钥，请联系管理员配置插件
```

### 视频不存在
```
输出: ❌ 获取视频信息失败，请检查BV号是否正确
```

### 无字幕
```
输出: ❌ 未找到可用的字幕
```

## 支持的链接格式

### 桌面版链接
- `https://www.bilibili.com/video/BV1jv7YzJED2`
- `https://www.bilibili.com/video/av123456`

### 手机版链接
- `https://m.bilibili.com/video/BV1jv7YzJED2`
- `https://m.bilibili.com/video/av123456`

### 短链接
- `https://b23.tv/xxxxx`

### 带参数的链接
- `https://www.bilibili.com/video/BV1jv7YzJED2?p=1&t=30`
- `https://www.bilibili.com/video/BV1jv7YzJED2?spm_id_from=333.999.0.0`

## 智能解析支持的消息类型

### 引用消息
- QQ引用消息
- 微信引用消息
- Telegram回复消息
- 其他平台的引用/回复消息

### 转发消息
- QQ转发消息
- 微信转发消息
- bilibili视频分享卡片
- 包含链接的合并转发消息

### 普通消息
- 包含bilibili链接的普通文本消息
- 混合文本和链接的消息
