# 更新日志

## [v1.2.0] - 2025-01-XX

### 新增功能
- ✨ **引用消息解析**: 支持引用包含bilibili链接的消息后使用 `/bs` 命令
- ✨ **转发消息解析**: 支持转发bilibili视频卡片或包含链接的消息后使用 `/bs` 命令
- ✨ **智能链接提取**: 自动从消息中提取bilibili链接，无需手动输入
- ✨ **多平台兼容**: 支持QQ、微信、Telegram等平台的引用和转发消息

### 改进
- 🔧 **更智能的帮助信息**: 当未找到链接时显示更详细的使用说明
- 🔧 **更好的错误处理**: 改进了引用消息和转发消息的错误处理机制
- 🔧 **日志优化**: 添加了更详细的调试日志信息

### 使用方式
```bash
# 新的使用方式
1. 引用包含bilibili链接的消息 + /bs
2. 转发bilibili视频卡片 + /bs
3. 在包含链接的消息中直接使用 /bs

# 原有方式仍然支持
/bs BV1jv7YzJED2
/bs https://www.bilibili.com/video/BV1jv7YzJED2
```

## [v1.1.0] - 2025-01-XX

### 新增功能
- ✨ **多种链接格式支持**: 支持BV号、AV号、完整链接、短链接
- ✨ **智能链接解析**: 自动识别和解析各种bilibili链接格式
- ✨ **短链接支持**: 支持b23.tv短链接的自动重定向解析
- ✨ **AV号转换**: 自动将AV号转换为BV号进行处理

### 改进
- 🔧 **用户体验优化**: 减少中间过程提示信息，只在完成后显示结果
- 🔧 **错误处理**: 改进了链接格式验证和错误提示
- 🔧 **性能优化**: 优化了API调用和请求处理流程

### 支持的链接格式
- BV号: `BV1jv7YzJED2` 或 `1jv7YzJED2`
- AV号: `av123456` 或 `123456`
- 桌面版: `https://www.bilibili.com/video/BV1jv7YzJED2`
- 手机版: `https://m.bilibili.com/video/BV1jv7YzJED2`
- 短链接: `https://b23.tv/xxxxx`

## [v1.0.0] - 2025-01-XX

### 初始版本
- ✨ **基础功能**: 支持通过BV号获取bilibili视频字幕
- ✨ **LLM总结**: 使用大语言模型生成视频内容总结
- ✨ **字幕优先级**: 优先选择中文字幕，支持多语言字幕
- ✨ **配置管理**: 支持OpenAI API和Bilibili SESSDATA配置
- ✨ **风控保护**: 内置请求间隔和错误处理机制

### 核心特性
- 🎬 获取bilibili视频字幕
- 🤖 LLM生成内容总结
- 🌏 中文字幕优先选择
- ⚙️ 可配置的API参数
- 🛡️ 风控保护机制

---

## 开发计划

### 未来版本规划

#### v1.3.0 (计划中)
- 🔮 **批量处理**: 支持一次处理多个视频链接
- 🔮 **总结模板**: 支持自定义总结格式和模板
- 🔮 **缓存机制**: 添加视频总结缓存，避免重复处理
- 🔮 **多语言支持**: 支持英文、日文等其他语言字幕

#### v1.4.0 (计划中)
- 🔮 **视频分析**: 支持无字幕视频的AI视觉分析
- 🔮 **关键词提取**: 从总结中提取关键词和标签
- 🔮 **总结评分**: 对总结质量进行评分和优化建议

### 贡献指南

欢迎提交Issue和Pull Request！

- 🐛 **Bug报告**: 请详细描述问题和复现步骤
- 💡 **功能建议**: 欢迎提出新功能的想法和建议
- 🔧 **代码贡献**: 请遵循项目的代码规范和测试要求

### 技术栈

- **Python 3.8+**
- **AstrBot Plugin Framework**
- **aiohttp**: 异步HTTP请求
- **OpenAI API**: LLM服务
- **Bilibili API**: 视频信息和字幕获取
