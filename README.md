# Bilibili视频字幕总结插件

这是一个AstrBot插件，可以获取Bilibili视频的字幕并使用LLM生成内容总结。

## 功能特性

- 🎬 支持通过BV号获取Bilibili视频字幕
- 🤖 使用LLM生成视频内容总结
- 🌏 优先选择中文字幕，支持多语言字幕
- ⚙️ 可配置的API参数和请求间隔
- 🛡️ 内置风控保护机制

## 使用方法

### 基本命令

```
/bs [BV号]
```

例如：
```
/bs BV1jv7YzJED2
/bs 1jv7YzJED2
```

### 配置说明

在AstrBot管理面板中配置以下参数：

#### 必需配置
- **OpenAI API密钥**: 用于调用LLM生成总结的API密钥
- **Bilibili SESSDATA**: 从浏览器Cookie中获取，用于访问需要登录的API

#### 可选配置
- **OpenAI API地址**: 默认为OpenAI官方地址，可配置为其他兼容接口
- **使用的模型**: 默认为gpt-3.5-turbo
- **请求间隔**: 两次API请求之间的间隔时间，避免触发风控
- **最大字幕长度**: 提交给LLM的字幕最大字符数
- **总结提示词**: 用于指导LLM生成总结的提示词

## 获取Bilibili SESSDATA

1. 打开浏览器，登录Bilibili
2. 按F12打开开发者工具
3. 切换到"Application"或"应用程序"标签
4. 在左侧找到"Cookies" -> "https://www.bilibili.com"
5. 找到名为"SESSDATA"的Cookie，复制其值
6. 将该值填入插件配置中

## 注意事项

- 获取字幕需要登录状态，请确保配置了有效的SESSDATA
- 请求过于频繁可能触发Bilibili的风控机制，建议适当设置请求间隔
- 部分视频可能没有字幕或字幕需要特殊权限
- 请遵守Bilibili的使用条款和相关法律法规

## 错误处理

插件会处理以下常见错误：
- BV号格式错误
- 视频不存在或无法访问
- 字幕获取失败
- LLM API调用失败
- 网络连接问题

## 技术实现

1. 通过BV号调用Bilibili API获取视频的aid和cid
2. 使用aid和cid获取字幕URL列表
3. 优先选择中文字幕，下载字幕内容
4. 结合视频标题、简介和字幕内容提交给LLM
5. 返回生成的视频内容总结

## 使用示例

输入：`/bs BV1jv7YzJED2`

输出：
```
🔍 正在处理视频 BV1jv7YzJED2，请稍候...

📺 视频标题：[示例视频标题]

📋 内容总结：
这是一个关于...的视频。主要内容包括：
1. ...
2. ...
```

## 版本历史

- v1.0.0: 初始版本，支持基本的字幕获取和总结功能
- v1.0.1: 优化用户体验，减少中间过程提示信息
