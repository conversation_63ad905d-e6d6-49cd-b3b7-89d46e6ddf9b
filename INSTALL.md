# 安装说明

## 前置要求

- AstrBot v3.4.0 或更高版本
- Python 3.8 或更高版本
- 有效的OpenAI API密钥或兼容的API服务
- Bilibili账号的SESSDATA Cookie

## 安装步骤

### 方法一：通过AstrBot插件管理器安装

1. 在AstrBot管理面板中，进入"插件管理"
2. 点击"安装插件"
3. 输入插件仓库地址：`https://github.com/user/astrbot_plugin_bilibili_summary`
4. 点击"安装"

### 方法二：手动安装

1. 克隆或下载插件代码到AstrBot的插件目录：
```bash
cd AstrBot/data/plugins
git clone https://github.com/user/astrbot_plugin_bilibili_summary
```

2. 重启AstrBot或在管理面板中重载插件

## 配置步骤

### 1. 获取OpenAI API密钥

- 访问 [OpenAI官网](https://platform.openai.com/api-keys)
- 创建新的API密钥
- 复制密钥备用

### 2. 获取Bilibili SESSDATA

1. 打开浏览器，访问 [bilibili.com](https://www.bilibili.com)
2. 登录你的Bilibili账号
3. 按F12打开开发者工具
4. 切换到"Application"或"应用程序"标签
5. 在左侧找到"Cookies" -> "https://www.bilibili.com"
6. 找到名为"SESSDATA"的Cookie
7. 复制其值（通常是一个很长的字符串）

### 3. 在AstrBot中配置插件

1. 进入AstrBot管理面板
2. 找到"Bilibili视频字幕总结插件"
3. 点击"配置"
4. 填入以下必需配置：
   - **OpenAI API密钥**: 步骤1中获取的API密钥
   - **Bilibili SESSDATA**: 步骤2中获取的Cookie值

5. 可选配置项：
   - **OpenAI API地址**: 如使用其他兼容服务，修改此地址
   - **使用的模型**: 默认gpt-3.5-turbo，可改为gpt-4等
   - **请求间隔**: 建议保持2秒以避免风控
   - **最大字幕长度**: 根据模型token限制调整
   - **总结提示词**: 自定义总结风格

6. 保存配置

## 验证安装

发送测试命令：
```
/bs BV1jv7YzJED2
```

如果配置正确，应该能看到插件开始工作并返回视频总结。

## 常见问题

### Q: 提示"未配置OpenAI API密钥"
A: 检查配置中的API密钥是否正确填写，确保没有多余的空格。

### Q: 提示"获取视频信息失败"
A: 检查BV号是否正确，或者视频是否存在/可访问。

### Q: 提示"未找到可用的字幕"
A: 该视频可能没有字幕，或者需要更高权限访问。确保SESSDATA配置正确。

### Q: 请求频繁被限制
A: 增加"请求间隔"配置，建议设置为3-5秒。

### Q: LLM API调用失败
A: 检查API密钥是否有效，API地址是否正确，账户是否有足够余额。

## 卸载

1. 在AstrBot管理面板中找到插件
2. 点击"卸载"
3. 或者手动删除插件目录：`AstrBot/data/plugins/astrbot_plugin_bilibili_summary`
