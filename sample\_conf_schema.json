{"personas": {"description": "多个系统提示词配置（JSON格式）", "type": "string", "default": "{\"default\": \"你是一个有帮助的AI助手。\", \"programmer\": \"你是一个专业的程序员，擅长解决编程问题和代码优化。\", \"teacher\": \"你是一个耐心的老师，善于用简单易懂的方式解释复杂概念。\", \"translator\": \"你是一个专业的翻译，能够准确翻译各种语言。\"}", "hint": "配置多个命名的系统提示词，JSON格式：{\"角色名\": \"提示词内容\"}"}, "api_keys": {"description": "多个API密钥配置（JSON格式）", "type": "string", "default": "{\"default\": \"\"}", "hint": "配置多个命名的API密钥，JSON格式：{\"密钥名\": \"密钥内容\"}"}, "context_length": {"description": "上下文消息数量", "type": "int", "default": 10, "hint": "保留的历史消息数量，用于维持对话上下文"}, "api_url": {"description": "API接口地址", "type": "string", "default": "https://www.gpt4novel.com/api/xiaoshuoai/ext/v1/chat/completions", "hint": "OpenAI兼容的API接口地址"}, "model": {"description": "使用的模型", "type": "string", "default": "nalang-turbo-v23", "hint": "要使用的AI模型名称"}, "temperature": {"description": "温度参数", "type": "float", "default": 0.7, "hint": "控制回复的随机性，0-1之间"}, "max_tokens": {"description": "最大token数", "type": "int", "default": 800, "hint": "单次回复的最大token数量"}, "top_p": {"description": "Top-p参数", "type": "float", "default": 0.35, "hint": "核采样参数，0-1之间"}, "repetition_penalty": {"description": "重复惩罚", "type": "float", "default": 1.05, "hint": "避免重复内容的惩罚系数"}, "show_nickname": {"description": "在群聊中显示用户昵称", "type": "bool", "default": true, "hint": "是否在群聊消息中包含用户昵称，帮助AI区分不同用户"}, "group_shared_context": {"description": "群聊共享上下文", "type": "bool", "default": true, "hint": "是否在群聊中让所有成员共享聊天上下文"}}